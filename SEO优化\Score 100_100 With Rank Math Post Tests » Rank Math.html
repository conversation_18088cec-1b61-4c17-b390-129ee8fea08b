<!DOCTYPE html>
<!-- saved from url=(0043)https://rankmath.com/kb/score-100-in-tests/ -->
<html lang="en-US" prefix="og: https://ogp.me/ns#"><plasmo-csui><template shadowrootmode="open"><div id="plasmo-shadow-container" style="z-index: 2147483647; position: relative;"><div id="plasmo-overlay-0" class="plasmo-csui-container" style="display: flex; position: absolute; top: 0px; left: 0px;"></div></div></template></plasmo-csui><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

			<h1 class="hkb-article__title">Score 100/100 With Rank Math Post Tests</h1>

			
			
<!-- .hkb-article__content -->
<div class="hkb-article__content">
	<p>In this tutorial, we will show you how to use Rank Math’s content analysis to optimize your posts, score a 100 on 100 in the tests, and set up your articles to receive the most traffic from search engines.</p><div class="youtube-container"><div class="youtube-player" data-id="JYs8MDl_bMY" data-image="exists" data-controls="1" role="button" tabindex="0" aria-label="Play Video"><div><img fetchpriority="high" decoding="async" class="youtube-thumb" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/maxresdefault.jpg" width="1280" height="720" alt="Score 100/100 in SEO" role="presentation"><div class="play-button" aria-hidden="true"><i class="icon-youtube"></i></div></div></div></div><p class="takeaway yellow"><strong>Note:</strong> You can now <a href="https://rankmath.com/kb/fix-seo-tests-with-content-ai/">use Content AI to score 100/100</a> with just a few clicks and start ranking higher.</p><p>When you create a new post, one of your goals is to attract traffic to your post, and Rank Math’s content analysis and optimization features are designed to achieve just that.</p><p>With each post you create, <strong>Rank Math assists you in optimizing your post to receive maximum traffic</strong> by giving you strategic recommendations based on your content. If you follow all the recommendations, then your post will be completely optimized from a content perspective, and the probability of your content ranking will be higher as well.</p><p>The process of optimization begins when you start creating the post. That is why all the tools for the optimization have been placed on the post-creation screen. To see all the tools available, either add a new post from the WordPress menu or edit an existing post. For our demonstration, we will create a new post from <strong>WordPress Menu → Posts → Add New</strong>.</p><figure class="wp-block-image size-large"><img decoding="async" width="324" height="370" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/add-new-post.jpg" alt="add new post" class="wp-image-790482"></figure><p>Rank Math fully supports the classic editor that everyone is accustomed to, and also the new block-editor. If you’re using the classic editor, you’ll have to scroll down below the text area to see Rank Math’s Meta Box.</p><figure class="wp-block-image size-large"><img decoding="async" width="1888" height="950" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/rank-math-meta-box-1.jpg" alt="rank math meta boxes old location" class="wp-image-790487"></figure><p>On the new block editor, Rank Math is not below the content area but is fully integrated into the block editor. To see Rank Math, click the SEO Score button on the top-right corner of the screen.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="2560" height="897" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/rank-math-block-editor-scaled.jpg" alt="how to open rank math block settings" class="wp-image-790505"></figure><p>And you will see all of Rank Math’s tools and suggestions in block view.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="2560" height="1225" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/example-block-settings-scaled.jpg" alt="example of rank math block-based settings" class="wp-image-790510"></figure><p>In this article, we will go through the process of writing an article in WordPress. Then we will optimize the post completely using Rank Math’s SEO custom recommendations.</p><div id="toc">
<h2 id="contents" class="rm-has-anchor-link">Table of Contents<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#contents" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2>
<ul>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#writing-the-post">Writing the Post</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#first-step-choosing-focus-keywords">First Step – Choosing Focus Keywords</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#google-trends-for-focus-keywords">Google Trends for Focus Keywords</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#understanding-the-tests-from-a-keyword-perspective">Understanding the Tests from a Keyword Perspective</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#pillar-content">Pillar Content</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#making-sense-of-rank-math-s-recommendations-the-color-codes">Making Sense of Rank Math’s Recommendations – The Color Codes</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#test-color-codes">Test Color Codes</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#basic-seo">Basic SEO</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#passing-all-basic-seo-tests">Passing All Basic SEO Tests</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#additional-seo">Additional SEO</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#passing-all-the-tests-in-the-additional-seo-section">Passing All the Tests in the Additional SEO Section</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#title-readability">Title Readability</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#passing-all-title-readability-tests">Passing All Title Readability Tests</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#content-readability">Content Readability</a></li>
<li><a href="https://rankmath.com/kb/score-100-in-tests/#scoring-a-full-100-for-seo-optimization">Scoring a Full 100 for SEO Optimization</a></li>
</ul>
</div><h2 class="wp-block-heading rm-has-anchor-link" id="writing-the-post"><span class="number">1</span>  Writing the Post<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#writing-the-post" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>The usual workflow for writing articles is to write them first, then optimize them. Some people also optimize the post while they write it, but we find the first method to be more efficient.</p><p>For this example, we will add an existing Rank Math KB to the post area. That should be interesting, as the KBs are written to be helpful for you but are not really optimized for SEO. The contrast will help us understand how Rank Math will help optimize posts, even if you are writing a post for the first time.</p><p>Do note that we are not talking about the formatting of your content here. We could dedicate a whole new post on that topic, but fortunately, there is already a good resource on <a class="rank-math-link" href="https://www.siegemedia.com/creation/blog-post-templates" target="_blank" rel="noopener">how to do proper formatting of your content</a>.</p><h2 class="wp-block-heading rm-has-anchor-link" id="first-step-choosing-focus-keywords"><span class="number">2</span>  First Step – Choosing Focus Keywords<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#first-step-choosing-focus-keywords" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>As we mentioned, we will use an existing Rank Math KB to demonstrate the optimization process. But you will have to write the actual post before you can experience how Rank Math works.</p><p>Once you’ve written the content, the first step in the optimization process is to choose the right focus keywords.</p><figure class="wp-block-image size-large is-resized"><img loading="lazy" decoding="async" width="596" height="386" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/focus-keyword.jpg" alt="presence of focus keyword settings in rank math" class="wp-image-790512" style="width:undefinedpx;height:387px"></figure><p>Focus keywords are the words you want your post to rank for. It can range from a single word, like “coffee”, to a whole phrase, like “play the ukulele”. </p><p>If you’ve ever done keyword research, you’ll have no difficulty understanding what you should keep as your focus keywords. The words that users type in the search engines are the best focus keywords to choose from. If you’re unfamiliar with the process, we recommend reading our <a href="https://rankmath.com/blog/keyword-research/" data-type="post" data-id="1221262">detailed guide on keyword research </a>to learn the process.</p><p>You might be thinking, if Rank Math is so powerful, <em>why do I need to set focus keywords? Shouldn’t Rank Math be able to figure it out?</em></p><p>Well, Rank Math is powerful, but it can only perfect your SEO recipe if you tell it what you want to cook. We mean that Rank Math does a lot of work; you just have to provide a direction by setting up some essentials.</p><p>For this example, we’ve added the content of the “<a href="https://rankmath.com/kb/what-are-http-redirections/">What are HTTP Redirections</a>” to our demo article. Keeping that in mind, we will use “HTTP Redirections” as our first focus keyword.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="2560" height="744" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/focus-keyword-example-scaled.jpg" alt="primary focus keyword added to rank math block" class="wp-image-790523"></figure><p><strong>Rank Math PRO lets you choose unlimited focus keywords per post.</strong> That means you can optimize your post for unlimited keywords and attract more traffic than if you were optimizing for 1 focus keyword.</p><p>However, do not get carried away with adding multiple keywords. Only add a reasonable number of keywords to optimize a single post for, and adding more focus keywords than necessary would do more harm than good. </p><p>You might also have noticed an error just below the focus keyword section.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="600" height="498" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/connect-rank-math-account.jpg" alt="link to connect your rank math account" class="wp-image-849915"></figure><p>The error is quite self-explanatory. If you activate your copy of Rank Math, then Rank Math can fetch keyword suggestions for you when you add focus keywords. These suggestions are fetched right from Google, and they can be a powerful way not only to discover related keywords but use those related keywords as focus keywords and optimize your post around them.</p><p>To connect your account, simply click the link in error, and you will be taken to the Rank Math activation screen.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="2118" height="740" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/connect-account.jpg" alt="Connect Rank Math account" class="wp-image-849921"></figure><p>Once you click the <strong>“Connect Now”</strong> button, you will be taken to Rank Math’s Login Page. Choose your preferred method to log in &amp; continue. If you don’t already have an account, you can create one by clicking the <strong>“Register Now”</strong> link at the bottom of the login form.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="1264" height="718" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/Rank-Math-login-form.png" alt="Rank Math login form" class="wp-image-1011573" style="width:900px;height:undefinedpx"></figure><p>After registering/logging in, you will be taken back to the Activation Screen, and your copy should be activated.</p><figure class="wp-block-image size-large is-resized"><img loading="lazy" decoding="async" width="2160" height="760" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/account-connected.jpg" alt="rank math suggestions from google" class="wp-image-849923" style="width:900px;height:undefinedpx"></figure><p>Now, head back to the post and refresh the page. Make sure to save your changes before refreshing. After the refresh, the error will be gone, and you will start seeing suggestions when you add any focus keywords to your posts, similar to the animation below.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="298" height="360" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/suggestions-from-google.gif" alt="suggestions from google" class="wp-image-849938"></figure><p>To add more focus keywords to your post, simply go to the focus keyword area and type in more focus keywords. The first focus keyword you add will be the <strong>Primary Focus Keyword</strong>, and the rest will be considered secondary focus keywords. The Primary Keyword will also have a “*” icon on its left.  Here is how it should appear on the screen.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="602" height="504" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/focus-keyword-1.jpg" alt="denotion of primary focus keyword in rank math" class="wp-image-796015"></figure><h2 class="wp-block-heading rm-has-anchor-link" id="google-trends-for-focus-keywords"><span class="number">3</span>  Google Trends for Focus Keywords <a href="https://rankmath.com/pricing/" class="pro-badge" target="_blank" style="vertical-align:" title="Rank Math Premium Plan"><span class="label">PRO</span></a><a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#google-trends-for-focus-keywords" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>When you are writing a post around a particular topic, you need all the help you can get to make a decision on whether the target you have chosen is good and if there is any traffic on that keyword.</p><p>Even more than that, how do you choose which keyword to target when you have to select one of two closely related keywords?</p><p>Enter Google Trends.</p><p>Google trends offer you a quick way to show how a term or a topic has fared against another term or topic over a period of time. It gives you an idea of what type of traffic to expect and, most of all, which keyword to target amongst a pool of closely related keywords or topics.</p><p>Rank Math brings that function right inside your WordPress dashboard. After you enter a focus keyword or a set of focus keywords, clicking on the Google Trends icon will bring up a graph showing more information on your selected keywords:</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1812" height="350" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/Google-Trends-Tool.jpg" alt="Google Trends" class="wp-image-790530"></figure><p>Once you have determined which focus keyword to use, choose it from the Trend screen and click <code>Close &amp; use selected keyword(s)</code> button to use the selected keyword as the focus keyword and remove the rest of the keywords you had added for checking the Trend.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1836" height="1148" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/google-trends.jpg" alt="Rank-Math-Trends" class="wp-image-790573"></figure><p>Other options offered in the Google Trends overlay are:</p><ul class="wp-block-list"><li>Choose your target country</li>

<li>Choose a date range between 7 days to 12 months.</li></ul><h2 class="wp-block-heading rm-has-anchor-link" id="understanding-the-tests-from-a-keyword-perspective"><span class="number">4</span>  Understanding the Tests from a Keyword Perspective<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#understanding-the-tests-from-a-keyword-perspective" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>From a user (or your perspective), having the post optimized for more than one keyword is great. It lets you target wider topics and aim for more traffic with each post.</p><p>But, from an optimization standpoint, it does propose some challenges.</p><p>For example, all the tests that <strong>Rank Math performs won’t work well if Rank Math considers all the focus keywords</strong> <strong>at the same time</strong>. Some tests only work well if you’ve decided on a single keyword, and you’ll see inconsistent results if performed on more than one keyword.</p><p>An obvious workaround is to use only a single keyword for the test, which defeats the purpose of having more than one focus keyword. To counter this problem, we’ve configured some tests to run on all focus keywords, while some tests work for only some of the keywords. Here is how we divide the tests.</p><ul class="wp-block-list"><li>Tests that run on all Focus Keywords</li>

<li>Tests that run only on the Primary Focus Keyword</li>

<li>Tests that run only on the Secondary focus keyword</li>

<li>Tests that run only on the content</li></ul><p>Let us explain the differences between each of them.  When we discuss the actual tests, we will identify which type of test it is.</p><p><strong>Tests that run on all Focus Keywords</strong></p><p>These are the tests that run on all focus keywords, and they may consider all the focus keywords at once, or individually. You won’t have to do anything special for the tests that consider all the focus keywords, but for the tests that consider only one test at a time, you’ll have to click the individual focus keywords to see their results and optimize them individually for the best results.</p><p>To check out which tests run on which keywords, you can click on any of the secondary keywords, and the tests will be filtered to show to the ones that run on that keyword. Here is a visual representation of that concept.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="286" height="590" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/Rank-Math-tests-specific-to-keywords.gif" alt="Rank Math Content Analysis tests specific to keywords" class="wp-image-1011585"></figure><h3 class="wp-block-heading rm-has-anchor-link" id="tests-that-run-only-on-the-primary-focus-keyword"><span class="number">4.1</span>  Tests that run only on the Primary Focus Keyword<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#tests-that-run-only-on-the-primary-focus-keyword" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>These are the tests that work <strong>only on the Focus Keyword</strong> and ignore the rest of the keywords altogether.</p><h3 class="wp-block-heading rm-has-anchor-link" id="tests-that-run-only-on-the-secondary-focus-keyword"><span class="number">4.2</span>  Tests that run only on the Secondary focus keyword<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#tests-that-run-only-on-the-secondary-focus-keyword" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>These are the tests that only consider the secondary focus keywords and ignore the primary focus keyword.</p><h3 class="wp-block-heading rm-has-anchor-link" id="tests-that-run-only-on-the-content"><span class="number">4.3</span>  Tests that run only on the Content<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#tests-that-run-only-on-the-content" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>There are several tests in Rank Math that don’t depend on focus keywords and analyze the content in different ways. For these tests, you don’t have to pay attention to the focus keywords, just follow the recommendation Rank Math gives with the test.</p><h2 class="wp-block-heading rm-has-anchor-link" id="pillar-content"><span class="number">5</span>  Pillar Content<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#pillar-content" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>Just below the focus keyword, we can see this tick box about pillar content.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1838" height="418" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/pillar-content.jpg" alt="Pillar Content" class="wp-image-790470"></figure><p> <strong>Rank Math</strong>&nbsp;has a feature where it can mark a piece of&nbsp;<strong>content</strong>&nbsp;as “<strong>Pillar Content</strong>” – which is usually used for posts. <strong>Pillar Content</strong>&nbsp;is an evergreen piece of&nbsp;<strong>content</strong>&nbsp;that is designed to answer age-old questions which are bound to be asked for years to come. If you’re not familiar with Pillar content, we would recommend that you read <a class="rank-math-link" href="https://rankmath.com/kb/pillar-content-internal-linking/"><em>How to Use Pillar Content to Build Internal Links</em></a>.</p><h2 class="wp-block-heading rm-has-anchor-link" id="making-sense-of-rank-math-s-recommendations-the-color-codes"><span class="number">6</span>  Making Sense of Rank Math’s Recommendations – The Color Codes<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#making-sense-of-rank-math-s-recommendations-the-color-codes" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>We’ve kept the recommendations’ language as simple as possible so that everyone, even if they are <a href="https://support.google.com/webmasters/answer/7451184?hl=en" target="_blank" rel="noopener">not familiar with SEO</a>, will be able to understand and follow the recommendations easily.</p><p>To help navigate the recommendations faster, we’ve used basic color codes to tell you what tests you’ve passed and what you still need to work on. Here is what the colors mean.</p><p><strong>Green Tick</strong>: A Green tick in front of a test means you’ve passed with flying colors.</p><p><strong>Yellow</strong>: If you see the color Yellow on a test, that means your post isn’t optimized for the test, and you should spend some time fixing it.</p><p><strong>Red Cross</strong>: If you see a Red Cross icon, then that means your post has performed poorly on this test. We would recommend that you spend some time to optimize the content before you publish it to maximize the chances of rankings and traffic.</p><p class="takeaway yellow"><strong>Important Note</strong>: Just because a post is optimized for certain keywords does not guarantee that it will rank well. Other factors like competition, domain authority, relevance, and hundreds of other factors determine a page’s rankings. Many of these factors will actually be out of your control. SEO is a moving target, and our recommendations are based on the data we have collected and our experience. There are no guarantees in SEO, and our goal with these recommendations is to improve the probability of your page to rank. Even a post with a lower score can rank well if all the other factors are optimized well. So, take these optimizations tests as a framework or guide, not as absolute answers.</p><p>Apart from color codes, Rank Math also gives your post a cumulative score based on how well the entire post is optimized. The score is visible on the button you clicked to open up Rank Math inside the post editor screen.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="1048" height="1142" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/rm-score.jpg" alt="overall post score given by rank math" class="wp-image-1421119"></figure><p>You might have noticed that this score is also color-coded. Depending on how you score, it might appear in Red, Yellow, or Green.</p><p><strong><span style="color: #99cc00"><span style="color:#58bb58" class="has-inline-color">Green [81+ Score]</span></span></strong></p><p>If your overall score is over 80, then the score will appear Green. When you see a green score, that means the post is ready to publish. If you’re just above 80, we would still recommend you to optimize your article further and get a score closer to 100.</p><p><strong><span style="color: #ffcc00"><span style="color:#bf890d" class="has-inline-color">Yellow [51-80 Score]</span></span></strong></p><p>You’ll see a Yellow overall score is your post scores somewhere between 51-80. It means that your post is not fully optimized, and there is plenty of room for improvement. We would recommend that you optimize the post a little more for a higher probability of ranking in the search results.</p><p><strong><span style="color: #ff0000"><span style="color:#e93f30" class="has-inline-color">Red [Below 50 Score]</span></span></strong></p><p>If your test score is Red, that means it scored below 50 – which is bad. As you can understand, that means your post is terribly optimized for your focus keywords. If you see Red, that means that your post is not even following basic SEO practices, let alone advanced optimizations. Obviously, we would recommend that you follow all the recommendations that Rank Math gives you, and get to a decent optimization score to better the chances of your post ranking higher.</p><h3 class="wp-block-heading rm-has-anchor-link" id="some-clarifications-on-the-optimization-score"><span class="number">6.1</span>  Some Clarifications on the Optimization Score<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#some-clarifications-on-the-optimization-score" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>If you’ve been in the SEO world for some time, then you understand that SEO is not an exact science. It is based on research, testing, experimenting, and experience. </p><p>The recommendations that Rank Math provides are based on all of these factors. The recommendations are great, but they are still not perfect. When writing a post, <strong>consider the recommendations as suggestions, not absolute rules</strong>. </p><p class="takeaway yellow">Your goal is to please the reader of the article first and the search engines second. We understand the desire to optimize every post 100%, but if it is detrimental to the user experience, you are doing more harm than good. Follow the recommendations, and use them, but don’t let them be the only guideline you use when writing any post.</p><h3 class="wp-block-heading rm-has-anchor-link" id="color-code-in-the-focus-keywords"><span class="number">6.2</span>  Color Code in the Focus Keywords<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#color-code-in-the-focus-keywords" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>A great way to quickly understand how your post is optimized for individual keywords is by looking at the focus keywords themselves. As you improve your post and optimize it for certain keywords, those keywords themselves will change color to represent how well the post is optimized.  Here is a demonstration.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="1048" height="1142" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/overall-score.jpg" alt="complete analysis of rank math scores and color codes" class="wp-image-1421121"></figure><p>In the image, you can clearly see that our post is moderately optimized for the term “HTTP Redirections”. Therefore, the keyword is highlighted with the Yellow color. On the other hand, the post is not optimized well for any of the secondary keywords; therefore, they are highlighted in Red. The overall post score is also in the moderate range, and the Yellow highlight references that.</p><p>Now that you understand how to read through the tests and recommendations, it’s time to get into the optimizations themselves. Since there are many tests, we’ve categorized them into 4 categories.</p><ul class="wp-block-list"><li>Basic SEO</li>

<li>Additional</li>

<li>Title Readability</li>

<li>Content Readability</li></ul><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="590" height="432" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/errors.jpg" alt="all optimization recommendation sections in rank math" class="wp-image-790601"></figure><p>Let us start learning with the Basic SEO tests first.</p><h2 class="wp-block-heading rm-has-anchor-link" id="test-color-codes"><span class="number">7</span>  Test Color Codes<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#test-color-codes" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>Each test shows a colored icon on their left side. Most of the tests show a completely solid green or a red-colored icon. But some of the tests like <strong>Content-Length</strong>, <strong>Keyword Density</strong>, and <strong>Image/Video presence</strong> shows partially filled icons depending on the percentage of the score acquired out of the total score assigned to that test.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="960" height="380" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/rank-math-test-score-system.jpg" alt="Rank Math test scoring system" class="wp-image-499417"></figure><h2 class="wp-block-heading rm-has-anchor-link" id="basic-seo"><span class="number">8</span>  Basic SEO<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#basic-seo" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>The Basic SEO section gives you some basic SEO tips to optimize your post. Just because the section is called Basic SEO doesn’t mean that the tips aren’t powerful. It’s called basic SEO because it reminds you to perform “basic” optimizations—basic referring to foundational, rather than simple.</p><p>There are different basic tests for primary and secondary keywords, and you <strong>have to pass all the basic SEO tests for your keywords</strong> to achieve a 100/100 score in Rank Math—so don’t ignore the basic tests. Here are the sections that you would see in the Basic SEO section.</p><h3 class="wp-block-heading rm-has-anchor-link" id="focus-keyword-in-the-seo-title-primary-focus-keyword-only"><span class="number">8.1</span>  Focus Keyword in the SEO Title (Primary Focus Keyword Only)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#focus-keyword-in-the-seo-title-primary-focus-keyword-only" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="574" height="122" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/focus-keyword-message.jpg" alt="focus keyword in seo title recommendation" class="wp-image-791107"></figure><p>The first test is about the focus keyword being present in the SEO Title of the page. <strong>This test only checks for your primary keyword in the SEO page title, as it’s not practical to have all your focus keywords in the title.</strong></p><p>As you already know, the presence of your <a href="https://www.wix.com/blog/2017/09/how-to-write-seo-title-tag/" target="_blank" rel="noopener">primary focus keyword in the title of the page</a> is an important ranking signal for Google and other search engines. Also, Google displays around 60 characters of your title on desktops and around 50 characters on mobile. Therefore, it’s important to add your primary focus keyword within 50 characters to be displayed on both desktops and mobile devices.</p><p>Most of the time, you would include the focus keyword in the title already, and this test will automatically pass. But, if you fail the test, head over to the title of the page and re-word it so that your primary focus keyword appears in the first 50 characters.</p><p>You can do that by editing the post’s title directly, or by editing the title in Rank Math’s Snippet editor. To do so, in the Block editor, click the <strong>Rank Math icon. </strong>Then in the General tab of Rank Math metabox, click <strong>Edit Snippet</strong>, as shown below.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="692" height="732" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/edit-snippet-block-editor.jpg" alt="Edit Snippet in Block Editor" class="wp-image-1143029"></figure><p><br>In the Classic editor, navigate to the Rank Math metabox and click <strong>Edit Snippet</strong>.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="1924" height="600" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/edit-snippet-classic-editor.jpg" alt="Click Edit Snippet in the Classic editor" class="wp-image-1143028" style="width:900px;height:undefinedpx"></figure><p>In the Preview Snippet Editor, enter the meta title containing the focus keyword, as shown below.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="1318" height="776" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/enter-title.jpg" alt="Enter the SEO meta title with focus keyword" class="wp-image-1143030" style="width:900px;height:undefinedpx"></figure><p>Regardless of your method, once Rank Math detects the primary focus keyword in the post title, the test will pass.</p><h3 class="wp-block-heading rm-has-anchor-link" id="focus-keyword-in-the-meta-description-primary-focus-keyword-only"><span class="number">8.2</span>  Focus Keyword in the Meta Description (Primary Focus Keyword Only)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#focus-keyword-in-the-meta-description-primary-focus-keyword-only" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="936" height="770" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/meta-description-1.jpg" alt="focus keyword not found in meta description test" class="wp-image-1458567" style="object-fit:cover"></figure><p>The SEO description is the snippet that appears just below the title in the SERP. Its job is two-fold.</p><p>It gives Google and other search engines a glimpse of what the post is about, which helps them make ranking decisions. It also gives the users a reason to click through to your website rather than click to any other website.</p><p>By <a href="https://searchenginewatch.com/2016/05/26/how-to-write-meta-descriptions-for-seo-with-good-and-bad-examples/" target="_blank" rel="noopener">including your focus keyword in the SEO Description</a>, you are taking care of both, which means better rankings and better traffic for you.</p><p>To understand how to pass this test, you have to understand how Rank Math evaluates this test as well. Obviously, Rank Math looks for your <strong>primary focus keyword</strong> in the SEO description, but that’s not all. If you haven’t entered an SEO description, Rank Math will create a description automatically and then test it to see if the focus keyword is included.</p><p>The generated SEO Description will use data from the following areas.</p><ul class="wp-block-list"><li>Custom Meta Description set for the post</li>

<li>Description Templates set in Titles and Meta settings in Rank Math</li>

<li>Post Excerpt</li>

<li>The first paragraph of the content in the post</li></ul><p>Rank Math will use the text <strong>in the order they are mentioned</strong>. </p><p>If you do not set a custom meta description, Rank Math will use the Description Template in your SEO Title &amp; Meta settings.&nbsp;</p><p>The Description Template is set to <strong>%excerpt%</strong> by default. This means Rank Math will use your Post Excerpt as your SEO meta description.&nbsp;</p><p>If you don’t set a Post Excerpt, Rank Math will auto generate one from a paragraph containing the primary focus keyword. If your paragraphs do not contain your primary keyword, Rank Math falls back to the first paragraph of the content. </p><p>To set a Post Excerpt in the Block editor, click the <strong>Settings </strong>icon and navigate to the <strong>Excerpt</strong> metabox, as shown below.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="750" height="792" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/excerpt.jpg" alt="Excerpt" class="wp-image-1142963"></figure><p>In the Classic editor, the Excerpt metabox will be available on your post-editing screen, as shown below.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="1938" height="364" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/excerpt-classic-editor.jpg" alt="Excerpt Metabox in Classic editor" class="wp-image-1142965" style="width:900px;height:undefinedpx"></figure><p>If the Excerpt metabox is unavailable, click <strong>Screen Options </strong>located at the top of the post editing screen and enable <strong>Excerpt</strong>, as shown below.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="2560" height="716" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/enable-excerpt.jpg" alt="Enable Excerpt option" class="wp-image-1142981" style="width:900px;height:undefinedpx"></figure><p>You can also change the default variable of your Description Template. To do so, head over to <strong>Rank Math SEO → Titles &amp; Meta</strong>.&nbsp;</p><p>For posts, navigate to<strong> Posts → Single Post Description</strong>. For pages, head to <strong>Pages → Single Page Description</strong>.<strong> </strong>Once done, click the downward arrow to reveal the available variables.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="2410" height="1294" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/select-variables.jpg" alt="Select preferred variables" class="wp-image-1142983" style="width:900px;height:undefinedpx"></figure><p class="takeaway yellow "><strong>Note: </strong>If you select the <strong>%excerpt_only% </strong>variable, Rank Math will strictly use the Post Excerpt you set for the post. If you do not set a Post Excerpt, Rank Math will not generate one from your content.</p><p>The easiest and recommended way to pass this test is to <strong>set a custom meta description for the post. </strong>It gives you more control, and you can optimize your description for users and search engines alike.</p><p>Here is how to do it.</p><p>In the post editing screen, click the <strong>Rank Math </strong>icon. Then, in the <strong>General</strong> tab of Rank Math metabox, click <strong>Edit Snippet</strong>.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="828" height="674" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/edit-snippet.jpg" alt="Navigate to Edit Snippet" class="wp-image-1142985"></figure><p>Once done, enter your custom meta description in the <strong>Description </strong>field, as shown below.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="1322" height="1314" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/enter-custom-description.jpg" alt="Enter custom description" class="wp-image-1142990" style="width:900px;height:undefinedpx"></figure><p>That said, whether you’re writing your meta description or want Rank Math to generate it from your content, your focus keyword should appear in the first 120 – 160 characters. If it does not, you will continue to see the “Focus Keyword not found in your SEO Meta Description” error.</p><p class="takeaway yellow"><strong>Important Note</strong>: If you’ve installed Rank Math on a website with plenty of posts already, you don’t have to go back and manually fix the meta descriptions of your posts. Rank Math is intelligent enough to generate a meta description based on your primary focus keyword. If a focus keyword is not found, <strong>Rank Math will use the excerpt text as the meta description</strong>. If even that’s not set, then Rank Math will pick the first paragraph of your text as the SEO description.</p><h3 class="wp-block-heading rm-has-anchor-link" id="focus-keyword-in-the-url-primary-focus-keyword-only"><span class="number">8.3</span>  Focus Keyword in the URL (Primary Focus Keyword Only)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#focus-keyword-in-the-url-primary-focus-keyword-only" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="736" height="96" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/focus-keyword-in-url.jpg" alt="focus keyword used in the URL optimization test" class="wp-image-796023"></figure><p>The <a href="https://moz.com/learn/seo/url" target="_blank" rel="noopener">presence of the focus keyword in the URL</a> is another ranking signal that all search engines use. It’s also important from a user’s perspective.</p><p>As users perform a search on a search engine, they subconsciously choose to click on one result. We don’t exactly know what a user is thinking, but we can safely assume that they are looking for relevance and authority. <a href="https://ahrefs.com/blog/on-page-seo/" target="_blank" rel="noopener">Presence of the focus keyword in the URL</a> is another way to show the users that the link they are about to click is about the topic they searched for, and the search engines like it too.</p><p>Obviously, this test is only performed for the primary focus keyword, as it is impractical to add all the focus keywords in your URL without making a mess.</p><p>Most of the time, WordPress is smart enough to add some keywords to the URL. The test generally passes because of that. But, if you failed this test for some reason, here are the steps you need to take to pass.</p><p>First, enable document settings in the block editor if they are not already enabled by clicking the gear icon in the top right area. The keyboard shortcut to open the settings is <strong>Ctrl + Shift + ,</strong> on Windows and <strong>Cmd + Shift + ,</strong> for Macs.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1578" height="848" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/click-gear-icon.jpg" alt="enable post setting in the block editor" class="wp-image-791119"></figure><p>From the settings that appear, click on the “Permalink” setting.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="598" height="808" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/click-permalink.jpg" alt="open permalink settings in wordpress" class="wp-image-791120"></figure><p>Re-write the Permalink and add the primary focus keyword in it. Make sure to save your post as a draft or update it, whatever you prefer.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="884" height="926" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/edit-permalink-1.jpg" alt="Edit Permalink in Block Editor" class="wp-image-791180"></figure><p>If you don’t see the Permalink setting, then your post is likely not saved. Save a draft of your post, and you will start seeing the Permalink setting.</p><p>Things are even simpler on the classic editor as the Permalink is displayed just below the title of the post. Click the edit button, edit the permalink, and save your post to pass this test.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1946" height="228" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/edit-permalink-classic-editor-2.jpg" alt="change-permalink-in-classic-editor" class="wp-image-791181"></figure><p>An important thing to note is that this test will not run if you’re not using friendly URLs. Since there is no possibility of adding keywords to plain or numeric URLs, this test will be disabled.</p><p>To check if you’ve enabled friendly URLs, head over to WordPress settings, and check your permalink settings. The existing URL created by WordPress should be a good indicator of your current settings. To update your settings, head over to WordPress permalink settings in <strong>Settings → Permalinks</strong>.</p><h3 class="wp-block-heading rm-has-anchor-link" id="focus-keyword-at-the-beginning-of-the-content"><span class="number">8.4</span>  Focus Keyword at the beginning of the content (Primary Focus Keyword Only)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#focus-keyword-at-the-beginning-of-the-content" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="836" height="820" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/focus-keyword-not-in-content.jpg" alt="focus keyword in the beginning of content optimization test" class="wp-image-1458572"></figure><p>Apart from the title, description, and URL, it’s also important to have the focus keyword at the beginning of the post. This reinforces to the search engines what your post is about and also reassures the users that they’ve clicked the correct link.</p><p>In this test, Rank Math checks your content to see if the <strong>primary focus keyword is present in the first 10% of your post</strong>. If it’s found, you pass the test.</p><p>However, there is an exception to the rule. In situations where the post itself is under 300 words, Rank Math will check the entire 300 words for your focus keywords. Once the post goes over 300 words, Rank Math will again check the first 10% of your post.</p><p>To pass this test, just make sure that your focus keyword is present in the first 10% of your post. You can also <a href="https://rankmath.com/kb/fix-seo-tests-with-content-ai/#focus-keyword-at-the-beginning">fix this test</a> by clicking the <strong>Fix with AI </strong>button, and Content AI will take care of the rest.</p><h3 class="wp-block-heading rm-has-anchor-link" id="focus-keyword-in-the-content-runs-of-all-focus-keywords"><span class="number">8.5</span>  Focus keyword in the Content (Runs on All Focus Keywords)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#focus-keyword-in-the-content-runs-of-all-focus-keywords" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="836" height="820" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/focusk-keyword-not-in-content.jpg" alt="focus keyword appearing in content optimization test" class="wp-image-1458575"></figure><p>This test is kind of obvious. Apart from all the places we’ve discussed, Rank Math also checks if your focus keywords are present in the content as well. Since Rank Math attempts to optimize your post for all your focus keywords, this test runs for all your focus keywords. </p><p>Rank Math also tries to find both the singular and plural versions of your keywords and consider them in the test. Currently, this feature is limited to the English language, and it utilizes a small word base—so it might not work all the time. If you have a relatively unknown focus keyword, then Rank Math might not be able to understand its plural version, in which case, it will look only for the singular version of the keyword.</p><p>To pass this test, all you have to do is make sure that all your focus keywords appear in your content. This shouldn’t be hard as the focus keywords should be the topic of the post as well, and you can naturally include them in the content.</p><p>One thing you should remember is that it’s easy to overdo this. Your ultimate goal is to attract visitors and encourage them to read your content. If your content has too many keywords, it will look unnatural to your readers. Even if the search engines find it better and send you marginally more traffic, your readers will be put off by excess keyword usage. </p><p>To avoid this, pass the test, then read the content on your own. If you feel that the post has excessive use of keywords, you should reduce your keyword usage and make the post sound natural. You can also click <strong>Fix with AI </strong>button, and Content AI will analyze your content and <a href="https://rankmath.com/kb/fix-seo-tests-with-content-ai/#focus-keyword-appearance-in-the-content">add the focus keyword</a> &nbsp;in relevant areas while maintaining readability and context.<br></p><h3 class="wp-block-heading rm-has-anchor-link" id="overall-content-length"><span class="number">8.6</span>  Overall Content Length<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#overall-content-length" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="836" height="820" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/long-content.jpg" alt="overall content length optimization test" class="wp-image-1458578"></figure><p>Since Google does not tell the world how their algorithm works, webmasters are forced to rely on gut, experience, and research to understand the mysteries behind Google Search.</p><p>There have been many studies that have shown that content length and higher rankings have a positive correlation. Maybe Google prefers pages with more content, or the long-form content gives you the opportunity to explain the topic better. Whatever the reason, your website rankings should improve once you start, including long-form content.</p><p>In this test, Rank Math scores your content based on its length. Here is a simple reference guide to remember.</p><ul class="wp-block-list"><li><strong>More than 2500 words</strong>: 100% score</li>

<li><strong>2000-2500 words</strong>: 70% score</li>

<li><strong>1500-2000 words</strong>: 60% score</li>

<li><strong>1000-1500 words</strong>: 40% score</li>

<li><strong>600-1000 words</strong>: 20% score</li>

<li><strong>Below 600 words</strong>: 0% score</li></ul><p>An obvious exception to this rule is product pages. It is rare to see a product page with thousands of words of content, and Rank Math understands that and excludes any product pages from this test.</p><p>To pass this test, make sure your content is at least 2500 words. You can pass the test with a lower word count too, but you won’t score 100% on the test. You can click on the <strong>Fix with AI </strong>button to <a href="https://rankmath.com/kb/fix-seo-tests-with-content-ai/#content-length">pass this SEO test</a>, and Content AI will analyze your content and enhance it as needed to ensure it meets or exceeds the required word count.</p><p><strong>Note:</strong> In the PRO version of Rank Math, WooCommerce users will see Rank Math’s content test recommending 200 words.</p><p class="takeaway"><strong>Content AI:</strong> When you use Content AI for optimizing your content, Rank Math would score your content for this test based on the <a href="https://rankmath.com/kb/how-to-use-content-ai/#word-count">word count recommendations</a> from Content AI. The above scoring list is not relevant when using Content AI.</p><h3 class="wp-block-heading rm-has-anchor-link" id="has-product-schema"><span class="number">8.7</span>  Use Product Schema <a href="https://rankmath.com/pricing/" class="pro-badge" target="_blank" style="vertical-align:" title="Rank Math Premium Plan"><span class="label">PRO</span></a><a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#has-product-schema" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>This test is available only for WooCommerce and Easy Digital Downloads products in the PRO version of Rank Math.</p><p>Rank Math automatically fetches the WooCommerce and Easy Digital Downloads product data and includes them in the Product Schema.</p><p>If you’ve configured Rank Math to add Product Schema for WooCommerce and Easy Digital Downloads, you’ll pass the test. We’ve dedicated knowledgebase tutorials to guide you with adding product data for <a href="https://rankmath.com/kb/woocommerce-product-schema/" data-type="ht_kb" data-id="759673">WooCommerce</a> and <a href="https://rankmath.com/kb/edd-product-schema/" data-type="ht_kb" data-id="785788">Easy Digital Downloads</a>.</p><h2 class="wp-block-heading rm-has-anchor-link" id="passing-all-basic-seo-tests"><span class="number">9</span>  Passing All Basic SEO Tests<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#passing-all-basic-seo-tests" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>Once you follow the recommendations and optimize your post, the Basic SEO section of Rank Math should appear like this:</p><figure class="wp-block-image size-large"><img decoding="async" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/Basic-SEO-tests-in-Rank-Math.png" alt="Passing basic SEO tests in Rank Math"></figure><p>Once you see this, your overall post score should also be at least 50.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="580" height="324" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/overall-score-1.jpg" alt="overall score after passing basic seo optimization tests" class="wp-image-791191"></figure><p>That’s all the recommendations in the Basic SEO section. Let us move to the Additional SEO recommendations.</p><h2 class="wp-block-heading rm-has-anchor-link" id="additional-seo"><span class="number">10</span>  Additional SEO<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#additional-seo" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>The Additional SEO section gives more actionable tips to optimize your post. We’ve placed these tips in the Additional SEO section because they don’t have as big of an impact as the Basic SEO tips. Think of the Basic SEO tips as the base of a pizza and the Additional SEO tips as the toppings. There is no pizza without the base, and the pizza isn’t as good without the toppings. The bottom line is you should use the Basic tips and the Additional tips for the most SEO impact. Here are the optimizations you should see in the Additional tips section.</p><h3 class="wp-block-heading rm-has-anchor-link" id="focus-keyword-in-subheading-primary-and-secondary-focus-keywords"><span class="number">10.1</span>  Focus Keyword in Subheading (Primary and Secondary Focus Keywords)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#focus-keyword-in-subheading-primary-and-secondary-focus-keywords" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>An often ignored on-page SEO factor is the presence of your focus keyword in the H2, H3, and other tags. While Google pays the most attention to H1, H2, and other tags are not ignored, they are important for many reasons.</p><p>Not only does including your keywords in your headings bring relevancy, but it also helps achieve site-links in the results. Site-links are quick links that appear below your search result that helps the user quickly jump to a specific article section. Here is how they look.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1308" height="396" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/sitelinks-example-1.jpg" alt="jump to links in SERP" class="wp-image-791192"></figure><p>Site-links not only help build authority in your visitor’s eyes, but they also help achieve a higher click-through rate, both of which are essential to your website’s growth.</p><p>In this test, Rank Math will check if you’ve included your focus keywords in your headings and pass the test if it finds them. This test runs on all your focus keywords, so make sure that you’re using all of them in your headings. As is the case with any SEO optimizations, try to naturally incorporate the keywords in your article instead of stuffing them just for the sake of it.</p><p>For instance, the focus keyword for our blog <em>FAQ Schema: A Practical (and EASY) Guide</em> is FAQ Schema. We added a subheading that includes the focus keyword.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="1870" height="399" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/focus-keyword-in-subheadings.jpg" alt="Focus keyword used in subheading" class="wp-image-1115048" style="width:900px;height:undefinedpx"></figure><p>An important thing to remember is that this test runs for <strong>both primary and secondary focus keywords</strong>, and you’ll need to incorporate both of them in your subheadings to achieve a full score. You can always check if you pass the test for a secondary keyword by clicking on it.</p><p class="takeaway yellow"><strong>Note:</strong> This test is not applicable for WooCommerce Products and Easy Digital Downloads in the PRO version of Rank Math.</p><h3 class="wp-block-heading rm-has-anchor-link" id="focus-keyword-in-image-alt-attributes-primary-focus-keyword-only"><span class="number">10.2</span>  Focus Keyword in Image Alt Attributes (Primary Focus Keyword Only)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#focus-keyword-in-image-alt-attributes-primary-focus-keyword-only" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>In this test, Rank Math looks for your primary focus keyword in your images’ ALT text. Both the singular and the plural version of the keyword are considered when performing this test, and the test passes if Rank Math finds any one of the variations in the ALT text.</p><p>Image ALT text is <em>supposed</em> to be the text that appears on the screen if the image fails to load. In the early days of the Internet, bandwidth was scarce, and even just a few kilobytes were considered gigantic. That is why having some alternate text that could take the place of an image that could not be downloaded was considered a good practice, as it helped the users understand the context of the image.</p><p>Search engines soon realized that since ALT text simply describes the image, they could read it to understand what the image was about. Think about it, what would be the ALT text of an image of Roger Federer?</p><p>As we mentioned, to pass this test, you need to have your primary focus keywords in the ALT text of your image. Here is how you do it if you didn’t already know it.</p><p>Click on the image you want to set the ALT text for in the block editor.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1190" height="614" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/click-image-2.jpg" alt="click image to see image options" class="wp-image-791195"></figure><p>The settings bar on the right should shift to “block” settings, which means you get the opportunity to change the image’s settings. You should see the Alt Text area in the first few settings, and all you have to do is add the Alt text there and save your post.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="2560" height="1169" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/save-post-scaled.jpg" alt="click image to see image options" class="wp-image-1421124"></figure><p>If you’re using the classic editor, the steps are slightly different. First, click the image you’d like to set the Alt text of. You should see a bunch of options appear on the image. Click the Pen Icon to open the image settings.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1842" height="738" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/alt-text-in-classic-editor.jpg" alt="add alt text in the classic editor" class="wp-image-791203"></figure><p>You should see Alternative Text as the first option, which is the same as the Alt text. Add your desired Alt Text there, and update the image settings.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="2314" height="1302" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/alt-text-classic-editor-2.jpg" alt="adding alt text and saving in classic editor" class="wp-image-791201"></figure><h3 class="wp-block-heading rm-has-anchor-link" id="keyword-density-primary-and-secondary-focus-keywords"><span class="number">10.3</span>  Keyword Density (Primary and Secondary Focus Keywords)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#keyword-density-primary-and-secondary-focus-keywords" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="738" height="412" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/keyword-density.jpg" alt="keyword density test failed" class="wp-image-1458584" style="object-fit:cover;width:undefinedpx;height:500px"></figure><p>Although Keyword Density is becoming less important, optimizing your post with a good keyword density will give you an edge in the search results.</p><p>However, Keyword Density should never be overdone. A low keyword density is better than a high keyword density. Even with a low keyword density, there is a good chance that Google will pick on your focus keyword. But, a high keyword density is a bad quality signal, which might get you kicked out from the search results.</p><p>While there is no perfect keyword density, <a href="https://moz.com/blog/how-much-keyword-repetition-is-optimal-whiteboard-friday" target="_blank" rel="noopener">a keyword density of 1-1.5% is sufficient in most cases</a>; the more important thing is to keep your language natural because if search engines don’t pick on the high keyword density, your users definitely will.</p><p>In this test, Rank Math checks how many times your focus keyword(s) and their combinations have been used in the post. Rank Math looks for both the primary focus keyword and secondary focus keywords to determine your keyword density.</p><p>To get full marks on this test, ensure your keyword density is between 1% and 1.5%. If you overdo it and exceed a keyword density of 2.5%, you’ll get a warning about that, too, and we saw that in the screenshot at the beginning of this topic.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1306" height="88" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/keyword-density-passed-1.jpg" alt="keyword density test passed" class="wp-image-791206"></figure><p class="takeaway"><strong>Content AI:</strong> When you use Content AI, Rank Math would sync this test with the keyword density of the <a href="https://rankmath.com/kb/how-to-use-content-ai/#keywords">first keyword suggested by Content AI to use in your content</a>. </p><h3 class="wp-block-heading rm-has-anchor-link" id="url-length"><span class="number">10.4</span>  URL Length<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#url-length" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="720" height="90" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/url-length-1.jpg" alt="URL length test" class="wp-image-791208"></figure><p><a href="https://moz.com/blog/15-seo-best-practices-for-structuring-urls" target="_blank" rel="noopener">Multiple URL length studies</a> have shown that shorter URLs have a higher chance of ranking in the SERPs. Short URLs are simple, to the point, and specific enough to drive clicks. Just think about it. Which of the following URLs look better to you?</p><pre class="wp-block-code"><code>www.site.com/best-plugin-to-do-search-engine-optimization-for-wordpress-sites/</code></pre><p>Or,</p><pre class="wp-block-code"><code>www.site.com/best-wordpress-seo-plugin</code></pre><p>The difference is clear and obvious.</p><p>Long URLs are also prone to be either keyword-stuffed or unnecessarily long. Google doesn’t prefer either one of them, and neither should you.</p><p>Although short URLs are preferred, there are no guidelines to say which URLs are considered “short”; therefore, it is not easy to quantify the length of a good URL. Instead of trying to hit a certain length, we recommend that you ask yourself, “Can I shorten the URL and make it more precise at the same time?” If the answer is yes, then you should reduce the length of the URL.</p><p>In this test, Rank Math checks the <strong>length of your URL</strong> and lets you know if it is too long. As we mentioned, it is not easy to specify an exact length that can be considered good, but in Rank Math’s case, that is the only option possible.</p><p>After careful testing and considering plenty of real-world examples, Rank Math’s “good” URL length has been set to less than or equal to <strong>75 characters</strong>. So, this test will pass if your URL length is equal to or less than 75 characters.</p><p>There are a couple of things to keep in mind with this test.</p><p>First, this test checks your entire URL, not just part of it. That means HTTP, your domain name, separator characters, and your post URL, all are included in the test.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1230" height="346" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/entire-url.jpg" alt="the-entire-url-is-considered-in-the-length-test" class="wp-image-791211"></figure><p>For this reason, if your domain name is too long, you should probably ignore this test. Because of the long domain name, you won’t be able to create a meaningful URL while pertaining to the 75 character limit. The test will appear as failed in that case, but you can safely assume that your post is optimized.</p><h3 class="wp-block-heading rm-has-anchor-link" id="linking-to-external-sources"><span class="number">10.5</span>  Linking to External Sources<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#linking-to-external-sources" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>You already know that links from other websites pointing to your website are great for your website’s SEO. What you might not have known is that links from your post to other websites are also, albeit small, a ranking factor.</p><p>Think about it this way. When you write a post, it’s natural to reference other articles, tools, websites, and research available elsewhere – just like we did in this article. <a href="https://www.searchenginejournal.com/study-shows-outgoing-links-have-positive-effects-on-seo/157431/" target="_blank" rel="noopener">Referencing them in your post</a> just makes your copy look natural to the search engines and the users. Just make sure not to link out to your direct competitors if you want to keep an edge in the SERPs.</p><p>In this test, Rank Math checks if you’re linking out to a few external websites. If you’re not, you’ll fail this test, and you will need to add relevant links to external websites to pass this test.</p><p><strong>Note:</strong> This test is not available for WooCommerce Products and Easy Digital Downloads in the PRO version of Rank Math.</p><p class="takeaway"><strong>Content AI:</strong> When you make use of Content AI, this test is synced with the <a href="https://rankmath.com/kb/how-to-use-content-ai/#link-count">External link count suggestions</a> from Content AI. Hence, if Content AI recommends not to add external links, this test would be hidden.</p><h3 class="wp-block-heading rm-has-anchor-link" id="linking-to-external-content-with-a-followed-link"><span class="number">10.6</span>  Linking to External Content with a Followed Link<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#linking-to-external-content-with-a-followed-link" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>If you’re familiar with SEO, then you know that not all links are created equal. There are “followed” links and “no-followed” links. Technically there are no “followed” links, as it is the default behavior of links, but the terminology on the Internet has developed in a way that saying a link is “followed” is considered normal.</p><p>In the initial days of the Internet, there was no “nofollow” or “dofollow”, all links were followed. This led to a massive spam problem where people would use automated software to create thousands of spam links to their sites. To combat this, Google introduced the <code>nofollow</code> tag.</p><p>This led to another problem. Earlier, websites linked to other websites freely and whenever it made sense. As soon as the nofollow tag was introduced, most websites started to nofollow all their links to external websites, trying to keep all the SEO power to themselves. This phenomenon was called <strong>PageRank Sculpting</strong>, and Google did not like it. After all, the entire purpose of the web (and their algorithm) was being defeated.</p><p>To combat this problem, they started rewarding sites linked to other websites without a nofollow tag, i.e., with followed links. And that is the reason you see this recommendation in Rank Math.</p><p>This test is dependent on the test above it. If you’re not linking out to external websites in the first place, then how would the link be followed?</p><p><strong>To pass this test, pass the test above it, and then make sure that at least one of your external links is followed.</strong></p><p>Now, you might wonder, doesn’t Rank Math have an option to automatically no-follow all external links? What if that option is enabled?</p><p>First things first—not all external links should be no-followed. We’ve added that option to make it easy to nofollow a lot of links, but not with the intention of no-following every external link. We encourage you link out to authoritative websites—that is why the hyperlink exists. When you’re linking to a trusted website, you should definitely not nofollow it. We’ve included a <strong>Whitelist</strong> feature in Rank Math, which helps you achieve that.</p><p>Here is how it works. You enable the feature to nofollow all external links. Then you add your list of trusted domains in the “Nofollow Exclude Domains” list. Once you save your settings, Rank Math will not nofollow any external links to any of those domains.</p><p>Another way to manage your links is <strong>not to enable</strong> the nofollow all external links option, and add the list of your <em>untrusted</em> domains to the <strong>blacklist</strong>, called the Nofollow Domains option in Rank Math. This will make all your external links followed, with the exception of the links to the domains listed in this section.</p><p>You can use either solution to manage your links, and if you link out more to trusted domains, then using the blacklist would save you time and vice versa.</p><p>The Blacklist and the Whitelist can be found in the <strong>General Settings</strong> inside Rank Math. You can learn all about them from the <a href="https://rankmath.com/kb/general-settings/">dedicated knowledge base article</a>.</p><p><strong>Note:</strong> This test is not applicable to WooCommerce Products and Easy Digital Downloads in the PRO version of Rank Math.</p><p class="takeaway"><strong>Content AI:</strong> When Content AI recommends not to add external links, it is obvious this test would not be relevant. Hence in such cases, Rank Math leaves this test hidden.</p><h3 class="wp-block-heading rm-has-anchor-link" id="linking-to-internal-resources"><span class="number">10.7</span>  Linking to Internal Resources<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#linking-to-internal-resources" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1048" height="94" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/external-linking.jpg" alt="linking to other websites test" class="wp-image-791214"></figure><p>Internal links are a powerful way to improve the SEO of your post and also give your users a better experience on your website. Rank Math checks your post in real-time and notifies you if your post does not have any internal links. The following types of links qualify for this test:</p><ul class="wp-block-list"><li>Links to other posts on your website</li>

<li>Links to other subdomains on your main domain</li>

<li>Links to the main domain from a sub-domain</li></ul><p>To pass this test, make sure to include a <a href="https://neilpatel.com/blog/commandments-of-internal-linking/" target="_blank" rel="noopener">few relevant internal links</a> from your post to other posts on your website.</p><p><strong>Note:</strong> This test is not applicable to WooCommerce Products and Easy Digital Downloads in the PRO version of Rank Math.</p><p class="takeaway"><strong>Content AI:</strong> This test is synced with the <a href="https://rankmath.com/kb/how-to-use-content-ai/#link-count">Internal link count suggestions</a> from Content AI. Hence, if Content AI recommends not to add internal links, Rank Math would hide this test.</p><h3 class="wp-block-heading rm-has-anchor-link" id="focus-keyword-uniqueness-primary-focus-keyword-only"><span class="number">10.8</span>  Focus Keyword Uniqueness (Primary Focus Keyword Only)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#focus-keyword-uniqueness-primary-focus-keyword-only" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="732" height="108" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/focus-keyword-not-used.jpg" alt="focus keyword unique test" class="wp-image-791217"></figure><p>This is a simple test that checks if you’ve already used your focus keyword in any of your other posts. The reason for this test should be quite obvious. If you use the same focus keyword on more than one post, that means you’re trying to rank more than one post for the same keywords. The SERPs are rarely easy enough to break into with 2 posts, and it is unlikely that both your posts will reach the top 10 spots.</p><p>Another problem with optimizing 2 posts for the same keywords is content uniqueness. If you’re writing about the same topic, making the content pieces unique would be tough. Even if you can achieve that, wouldn’t it be better just to have all the content in a single post?</p><p>By creating 2 posts about the same topic, you’re also opening up the <strong>possibility of creating duplicate content.</strong> Although search engines won’t penalize you for duplicate content, duplicate content can end up confusing them as to which of the posts they should rank.</p><p>Let us take an example. Suppose you created a post on your website sometime back targeting a specific keyword. Over time, the topic evolved, and you needed to cover the same content in more detail, so you wrote a new post on the same topic. Now think from a search engine’s perspective. According to them, an older piece of content that has (hopefully) some good links is a good resource compared to a brand-new one. Because of this, search engines will end up ranking your old content, and your new content will be cannibalized. For these reasons, we recommend creating only a single post for a focus keyword.</p><p>For new websites, it is unlikely that you will fail this test, as Rank Math notifies you. But, if you have an older website where a single topic has been covered multiple times, there are a few solutions that you can use to circumvent the situation. You can either merge the posts together or delete one of the posts. If you decide to delete the post, make sure to redirect the old post to the new one.</p><p>Before you decide which steps you’ll take to solve the problem, you have to find the post with the same focus keyword first. Don’t worry; you don’t have to go through all your posts and check each of them manually—Rank Math will help you out.</p><p>If you fail this test, Rank Math will not only notify you of the problem, but it will also add a link that you’ll be able to click to see all the posts that use the same focus keyword. Once you see all the posts, the rest of the steps are easy to implement. Here is how the error looks like:</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="520" height="184" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/already-used-focus-keyword.jpg" alt="link to see other posts using the focus keyword" class="wp-image-791219"></figure><p>When you click the link, this is how Rank Math presents the other posts using the same focus keyword as the main post.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="1399" height="462" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/details-of-other-posts-using-the-same-focus-keyword.png" alt="details of other posts using the same focus keyword" class="wp-image-1405113" style="width:840px;height:auto"></figure><p>Now, review the posts to identify any that are nearly identical to the main article. If there are, compare them with the main post using criteria like the SEO score from Rank Math, traffic, links, and conversions to decide whether to merge them or delete one.</p><p>However, if posts with the same keywords discuss a different topic from the main post, you may need to adjust the content and change the focus keyword. Consider targeting a different keyword to highlight the post’s uniqueness, or use a variation. For example, if the focus keyword is ‘Digital Marketing,’ you could use ‘Digital Marketing Strategies’ for one of the posts.”</p><h3 class="wp-block-heading rm-has-anchor-link" id="used-content-ai"><span class="number">10.9</span>  Use Content AI for Optimizing Post<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#used-content-ai" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="910" height="124" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/Content-AI-test-cleared.png" alt="Content AI test cleared" class="wp-image-1032819"></figure><p>In this test, Rank Math will check if you’re using Content AI to optimize your post for the focus keyword.</p><p>Rank Math’s <a href="https://rankmath.com/kb/how-to-use-content-ai/" data-type="ht_kb" data-id="998310">Content AI</a> is built to help users optimize posts for a specific focus keyword. You can research the focus keyword with our Content AI. Our AI would use our proprietary algorithms to research existing articles ranking for this focus keyword to deliver your intelligent suggestions for optimal word count, media count, heading count, and link count.</p><p>Our AI would also identify keywords you could use in your content, Headings, SEO Title, and Description. Content AI also suggests the FAQ questions that you include to answer the questions that users typically have in their minds while browsing these topics.</p><p>When you are using Content AI, you get additional score points. Content AI syncs with some of the tests in the SEO Score so the recommendations are tailored to your focus keywords. Normally, the SEO Score would take hard-coded numbers into consideration when using a test like the one for <a href="https://rankmath.com/kb/score-100-in-tests/#overall-content-length">content length</a>. But when you are using Content AI, we actually look at the content length of top ranking keywords and make suggestions that based on your keyword, rather than generic industry standards. So, using the Content AI makes the SEO Scoring more “intelligent”. </p><h3 class="wp-block-heading rm-has-anchor-link" id="is-review-enabled"><span class="number">10.10</span>  Allow Customers to Leave Reviews <a href="https://rankmath.com/pricing/" class="pro-badge" target="_blank" style="vertical-align:" title="Rank Math Premium Plan"><span class="label">PRO</span></a><a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#is-review-enabled" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>Reviews from your customers increase the credibility of your products, so Rank Math checks if you’ve enabled reviews in your WooCommerce and Easy Digital Downloads store. </p><p>While the default WooCommerce review &amp; Easy Digital Downloads review features are limited, users can consider using <a href="https://mythemeshop.com/plugins/wordpress-review/" target="_blank" rel="noopener">WP Review PRO</a> to improve the review form and encourage customers to leave reviews.</p><p class="takeaway yellow"><strong>Note:</strong> This test is applicable only for WooCommerce Products and Easy Digital Downloads in the PRO version of Rank Math.</p><h2 class="wp-block-heading rm-has-anchor-link" id="passing-all-the-tests-in-the-additional-seo-section"><span class="number">11</span>  Passing All the Tests in the Additional SEO Section<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#passing-all-the-tests-in-the-additional-seo-section" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>Once you follow all the steps we’ve outlined above and optimized your post, you should easily pass all the tests in the Additional SEO section. You should see all green ticks in the section, like this:</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="592" height="956" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/additional-seo-tests.jpg" alt="Passing Additional SEO tests in Rank Math" class="wp-image-1115034"></figure><p>Also, once you’ve passed all the Basic and Additional Tests, your SEO score for the primary focus keyword should be very high already, and you should start seeing the Green color for your score.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="1124" height="846" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/seo-score-green.jpg" alt="the overall score for primary focus keyword rank math block" class="wp-image-1421127"></figure><p>Let us get to the next section.</p><h2 class="wp-block-heading rm-has-anchor-link" id="title-readability"><span class="number">12</span>  Title Readability<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#title-readability" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>If you spend most of your resources on writing your content and very little resources on writing your titles, and then you should seriously reconsider your strategy.</p><p>It is a common saying in the advertising world that once you write your headline, you’ve spent 50 cents of your dollar. What that means is of their total copywriting budget, half is spent <strong>just on the title</strong>.</p><p>Why would they do that? Because, 100% of your audience might read their title, but <strong>less than 50% will actually read the content.</strong> And, this fact applies to well-written titles. A bad title might not even get 50% of readers.</p><p>The importance of good titles on your posts cannot be overstated. Not only is the title the hook that brings the readers in, but it also affects search rankings in ways you might not have considered.</p><p>Even though Google has never confirmed it, it is a well-known fact that Google uses click-through rates as a search ranking factor. Although that factor alone will not guarantee you excellent results, when you’re struggling to move up a couple of positions in the top 10 results, a higher click-through rate can be the edge that takes you above your competition.</p><p>When you write compelling titles that users cannot help but click, not only do you beat your competition (even though rank below them), but you also show Google that your result is more relevant to the query. If you continue to exceed the benchmark click-through rate for the query, Google will soon promote your result above others, and it will continue to stay there if it keeps attracting the majority of clicks.</p><p>Now that you understand all the benefits of writing better titles, let us get into the Title Readability tests that Rank Math performs on your post.</p><h3 class="wp-block-heading rm-has-anchor-link" id="focus-keyword-at-the-beginning-of-the-seo-title-only-for-primary-keyword"><span class="number">12.1</span>  Focus Keyword at the Beginning of the SEO Title (Only for Primary Keyword)<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#focus-keyword-at-the-beginning-of-the-seo-title-only-for-primary-keyword" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="592" height="112" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/Focus-keyword-used-at-the-beginning-of-the-SEO-title.png" alt="Focus keyword used at the beginning of the SEO title" class="wp-image-1011607"></figure><p>In the SERPs, you have to capture your user’s attention as quickly as possible. That is why it is important to phrase your title in a way that the important words (the focus keyword) appear at the beginning. Results with keywords at the beginning of the title are also favored slightly by the search engines.</p><p>In this test, Rank Math checks if you’ve placed your primary focus keyword in the first 50% of the title, and passes the test if you have. The reason why Rank Math checks the first 50% of the title and not the absolute beginning of the title is that sometimes it is not practical to place your focus keyword at the beginning of your post without it appearing unnatural. To account for those scenarios, Rank Math has a more relaxed limit on where it checks for your focus keywords.</p><p>If you fail this test, then all you have to do is re-write the title in a way that you incorporate the primary focus keyword in the beginning. Here is a practical example of a non-optimized title.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1994" height="874" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/example-of-failed-focus-keyword.jpg" alt="example of a failed test for a focus keyword" class="wp-image-791254"></figure><p>And here is how you should optimize your title to move the focus keyword at the beginning.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="2280" height="700" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/passed-test-focus-keyword.jpg" alt="the title is rewritten to pass the test" class="wp-image-791255"></figure><h3 class="wp-block-heading rm-has-anchor-link" id="sentiment-in-a-title"><span class="number">12.2</span>  Sentiment in a Title<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#sentiment-in-a-title" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>A proven way to get more people to click on your website in the SERPs is to have a title that evokes strong emotions. Notice that we didn’t say kind of emotions, just <em>strong</em> emotions. That is, it doesn’t matter what kind of emotions are evoked as long as the emotions are strong enough.</p><p><strong>A simple rule of copywriting is to evoke emotions.</strong> If we were talking about a pizza, we would tell you how soft the bread is, how wonderful it smells, how the toppings look and taste, and how delicious the cheese tastes in the mouth. See what we did there?</p><p>You should aim to do the same with the title. But, the title isn’t a place where you have a lot of text. Therefore, it’s ideal to have a title that evokes emotions.</p><p>If you’ve browsed Facebook, <strong>you might have come across several posts that have titles similar</strong> to – “7 life-hacks that will blow your mind, 4th one is the best!!!”. They are trying to evoke your curiosity–another strong emotion.</p><p>An important thing to remember when using this tactic is that you have to manage expectations. Making exaggerations might get users to click, but if your content does not deliver, then your users will be quick to exit your website and never return. The practice of writing exaggerated titles is called “clickbait”, as in, you’re literally baiting your users to click your results.</p><p>We would advise you to stay away from click-baiting for the long-term health of your website. Not only do clickbaity titles perform worse on social media when compared to other regular titles, but they also are considered a negative ranking factor. Not directly, but when someone lands on your website and quickly bounces back and goes to another result, that phenomenon is called <strong>pogo-sticking</strong>, and that is a well-known negative ranking factor in the eyes of search engines.</p><p>To pass this test, make sure that your title evokes a strong emotion from within the user. Sometimes, it is easier said than done, but many times, just replacing a few words from your title should do the trick. Here is an example.</p><p>This is the title we created in the last test, which fails this test.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="742" height="580" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/title-seo-test.jpg" alt="failed test for sentiment in the title" class="wp-image-1458591"></figure><p>And here is how we rephrased the title to make it more emotion-evoking.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="646" height="290" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/adding-power-words-for-sentiment-in-title.jpg" alt="adding power words for sentiment in the title" class="wp-image-791263"></figure><p>Sentiment Analysis is a complex subject, but its basis lies in understanding how certain words affect the emotion of a sentence. Here is a reference list of <a href="https://gist.github.com/mkulakowski2/4289437" target="_blank" rel="noopener">positive words</a> and <a href="https://github.com/jeffreybreen/twitter-sentiment-analysis-tutorial-201107/blob/master/data/opinion-lexicon-English/negative-words.txt" target="_blank" rel="noopener">negative words</a> that you can use in your titles to evoke a positive or negative response. You can also <a href="https://rankmath.com/kb/fix-seo-tests-with-content-ai/#positive-or-negative-sentiment-word">fix this SEO test</a> with the help of Content AI.</p><p class="takeaway yellow"><strong>Note:</strong> This test is not applicable for WooCommerce Products and Easy Digital Downloads in the PRO version of Rank Math.</p><h3 class="wp-block-heading rm-has-anchor-link" id="use-of-power-word-in-title"><span class="number">12.3</span>  Use of Power Word in Title<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#use-of-power-word-in-title" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>What if you had the magical powers of making anyone click your links in the SERPs? A power so good that it would almost be considered illegal?</p><p>Don’t worry. We don’t practice black magic. We were just demonstrating the amazing use of Power Words. These are words that, when used in titles, compel users to click them, whether they like it or not.</p><p>Think about it this way. What if in the initial paragraph, we said, “Make anyone click your links in the SERPs” instead of “Having the magical power of making anyone click your links”. The words mean the same thing, but the impact they have on the message is vastly different. That’s power words for you.</p><p>In this test, Rank Math checks if you’re using a power word in the title. The test is performed using an internal list of power words, which is regularly updated. If a power word is found, you pass the test; else, you fail the test.</p><p>To learn more about power words, we recommend that you read this <a class="rank-math-link" href="https://rankmath.com/blog/power-words/">amazing guide about power words</a>.</p><h3 class="wp-block-heading rm-has-anchor-link" id="number-in-title"><span class="number">12.4</span>  Number in Title<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#number-in-title" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><p>A proven way to get more clicks from the SERPs is to use numbers in the title. This technique has been popular for some time, but its effect has not faded as clickbait’s has. It is hard to explain <em>why</em> this works, but it is easy just to acknowledge that it works and use it to your advantage. Think about it, what sounds more <em>clickable</em> – “How to clean your garage?” or “7 easy ways to clean your garage?”. It’s obviously the second one.</p><p>In this test, <strong>Rank Math checks if you’re using numbers in your title</strong>. If you’re not, Rank Math will warn you that you should. So, to pass this test, all you have to do is include a number in the title of the post, and you’ll pass the test.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="756" height="107" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/number-in-seo-title.jpg" alt="using the number in title test" class="wp-image-791267"></figure><p>PS: Buzzsumo analyzed millions of headlines and found some interesting data about what kind of headlines performed better. We found the <a href="https://buzzsumo.com/blog/most-shared-headlines-study/#numbers" target="_blank" rel="noopener">section on using numbers in the title</a>, especially insightful.</p><p class="takeaway yellow"><strong>Note:</strong> This test is not applicable to WooCommerce Products and Easy Digital Downloads in the PRO version of Rank Math.</p><h2 class="wp-block-heading rm-has-anchor-link" id="passing-all-title-readability-tests"><span class="number">13</span>  Passing All Title Readability Tests<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#passing-all-title-readability-tests" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>Once you pass all Title Readability tests, the screen should be something like this.</p><figure class="wp-block-image size-large"><img decoding="async" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/Title-readability-tests-in-Rank-Math.png" alt="Passing all title readability tests"></figure><h2 class="wp-block-heading rm-has-anchor-link" id="content-readability"><span class="number">14</span>  Content Readability<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#content-readability" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>Up till now, you’ve used Rank Math to optimize your content and your title. So, you’ve optimized your content for the search engines and attracted your users with eye-catching titles.</p><p>Now that the user is on your website reading your post, what else can you do to improve their experience?</p><p>The answer is the <strong>presentation of the content</strong>. Think of it like this. Just because you’ve liked the trailer of a movie, that’s no guarantee that you’ll like the movie, right?</p><p>By improving the presentation of the post, you’re helping users reach your post and <strong>stay there</strong>. We’ve highlighted that on purpose–user experience is extremely important for the long-term health of your website.</p><p>First, a user who is happy to read your content can become a long-time reader and a subscriber. That is supremely beneficial for you.</p><p>Second, time spent on site is one of the ways Google and other search engines measure user engagement. And it shouldn’t be a surprise that websites with better engagement get higher rankings and more traffic.</p><p>The next few tests help you organize and present your content in ways that delight your readers. It’s the last part of the optimization process, but it is as important as the others. Let’s look at the tests.</p><h3 class="wp-block-heading rm-has-anchor-link" id="table-of-contents"><span class="number">14.1</span>  Use a Table of Contents to Present Your Content Better<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#table-of-contents" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="590" height="132" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/use-toc.jpg" alt="table of contents check rank math optimization" class="wp-image-791272"></figure><p>If you’re writing a long post, it’s essential to make the navigation of the post easier. Not all users are looking for the complete information that your post provides. Some users might be looking for a very specific answer that is buried somewhere in the post.</p><p>The easiest way to help your users is by <a href="https://marketever.com/on-page-seo-guide/#AddTable-of-Contents" class="rank-math-link" target="_blank" rel="noopener">introducing a table of contents in your post</a>. Just as a large book becomes easier to navigate with an index or table of contents, your post will also become easier to navigate with the <strong>inclusion of a table of contents</strong>.</p><p>Your effort will not go unnoticed by search engines. They will notice the engagement and reward you with better rankings. Not only that, <strong>Google may include a “Jump To” link in the SERPs</strong> when it detects a table of contents in your post. This massively improves your click-through rate. Just remember that Google does it on its discretion, like rich snippets, and simply including a table of contents is no guarantee that you will get the “Jump To” links. Still, not including a table of contents guarantees that you won’t “Jump to” links, but including them just might get you the links.</p><p>Here is an example of “Jump To” links.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1318" height="288" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/jump-links.jpg" alt="jump to options in SERPs using a table of contents" class="wp-image-791276"></figure><p>In this test, Rank Math checks for the presence of a table of contents creation plugin on your website. If you’re using the Block/Gutenberg Editor, you can use Rank Math’s <a href="https://rankmath.com/kb/table-of-contents-block/">Table of Contents</a> block; otherwise, you can check the compatible plugins. </p><p>You can quickly add Rank Math’s Table of Contents in the post-editing screen. Click the&nbsp;<strong>‘+ ‘</strong>&nbsp;icon and locate the&nbsp;<strong>Table of Contents by Rank Math</strong>&nbsp;block.</p><figure class="wp-block-image size-full is-resized"><img loading="lazy" decoding="async" width="2026" height="816" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/add-toc-rank-math-1.jpg" alt="Add Rank Math&#39;s Table of Contents Block" class="wp-image-1115306" style="width:900px;height:undefinedpx"></figure><p>Table of Contents by Rank Math allows you to add a smooth table of contents block to the page/post and a number of other features that help seamlessly build your customized block section.</p><p>On the assumption that you will use a TOC if you have the plugin installed, Rank Math will pass the test if it finds any of the following plugins installed on your website.</p><ul class="wp-block-list"><li><a href="https://rankmath.com/kb/table-of-contents-block/">Rank Math</a> <em>(use Rank Math Table of Contents block to clear the test</em>)</li>

<li><a class="rank-math-link rank-math-link" href="https://mythemeshop.com/plugins/wordpress-shortcode/" target="_blank" rel="noopener">WP Shortcode Pro</a></li>

<li><a class="rank-math-link" href="https://mythemeshop.com/plugins/wordpress-shortcode/" target="_blank" rel="noopener">WP Shortcode</a></li>

<li><a class="rank-math-link" href="https://rankmath.com/compatibility/toc-pack-table-of-contents-for-elementor/">TOC Pack – Table of Contents for Elementor</a></li>

<li><a class="rank-math-link rank-math-link" href="https://rankmath.com/compatibility/ultimate-blocks/">Ultimate Blocks</a></li>

<li><a class="rank-math-link rank-math-link" href="https://rankmath.com/compatibility/luckywp-table-of-contents/">LuckyWP Table of Contents</a></li>

<li><a class="rank-math-link" href="https://rankmath.com/compatibility/elementor/">Elementor Pro</a></li>

<li><a href="https://wordpress.org/plugins/table-of-contents-plus/" target="_blank" rel="noopener">Table of Contents Plus</a></li>

<li><a href="https://wordpress.org/plugins/easy-table-of-contents/" target="_blank" rel="noopener">Easy Table of Contents</a></li>

<li><a href="https://rankmath.com/compatibility/divi-table-of-contents-maker/">Divi Table Of Contents Maker</a></li>

<li><a href="https://rankmath.com/compatibility/top-table-of-contents/">TOP Table of Contents</a></li></ul><p>If you are using any other plugin for adding a Table of Contents, please contact your plugin/provider and ask them to <a class="rank-math-link rank-math-link" href="https://rankmath.com/kb/filters-hooks-api-developer/#add-toc-plugin"><strong>add this filter</strong></a> to their plugin/theme so Rank Math can detect that you are using a Table of Contents section in your posts. Once they add the filter, we will be happy to add them to the <a class="rank-math-link" href="https://rankmath.com/compatibility/">compatibility list here</a>.</p><p>If you are manually adding a hard-coded Table of Contents section, you can <a class="rank-math-link" href="https://rankmath.com/kb/filters-hooks-api-developer/#remove-content-analysis">use this filter to disable that particular test</a>.</p><p class="takeaway yellow"><strong>Note:</strong> This test is not applicable for WooCommerce Products and Easy Digital Downloads in the PRO version of Rank Math.</p><h3 class="wp-block-heading rm-has-anchor-link" id="use-of-short-paragraphs"><span class="number">14.2</span>  Use of Short Paragraphs<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#use-of-short-paragraphs" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="590" height="75" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/using-short-para.jpg" alt="using short paragraphs test" class="wp-image-791281"></figure><p>In the quest for better user experience, we’ve recommended you add a table of contents, images, and even simplify your language. The only thing that remains is how you organize your content. That’s what this test is about.</p><p>As we mentioned in another test above, putting up a wall of text is the fastest way to lose your readers’ interest. A great way to keep the user engaged is to use shorter paragraphs. Go through this very article, and you’ll notice that we keep our paragraphs limited to a few lines each.</p><p><strong>In this test, Rank Math analyzes your content and notifies you if any of your paragraphs have more than 120 words in it. If it finds one or more paragraphs with more than 120 words, this test will fail.</strong></p><p>When the test fails, Rank Math displays an <strong>eye</strong> icon. </p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="2560" height="1150" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/long-paragraph-test-scaled.jpg" alt="Short paragraph test" class="wp-image-1421130"></figure><p>Clicking this icon will highlight the lengthy paragraphs in Classic and Block editor. Additionally, we provide a <strong>Shorten with AI </strong>option for these extensive paragraphs, which utilizes our <a href="https://rankmath.com/kb/how-to-use-content-ai/">Content AI</a> to generate a more concise version.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="2560" height="1214" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/shorten-with-ai-scaled.jpg" alt="Shorten with AI" class="wp-image-1421133"></figure><h3 class="wp-block-heading rm-has-anchor-link" id="use-of-media-in-your-posts"><span class="number">14.3</span>  Use of Media in your posts<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#use-of-media-in-your-posts" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="852" height="122" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/conatins-images.jpg" alt="using media in post-test" class="wp-image-791284"></figure><p>The reason why more people watch movies rather than read books is simple. Videos are easy to consume, and they have a bigger impact. <a href="https://static.googleusercontent.com/media/www.google.com/en/us/insidesearch/howsearchworks/https://rankmath.com/wp-content/uploads/2019/12/searchqualityevaluatorguidelines.pdf">Several studies</a> point to the following facts about retention. Humans retain:</p><ul class="wp-block-list"><li>10 percent of what they READ</li>

<li>20 percent of what they HEAR</li>

<li>30 percent of what they SEE</li>

<li>50 percent of what they SEE and HEAR</li>

<li>70 percent of what they SAY and WRITE</li>

<li>90 percent of what they DO</li></ul><p>That is why it is critical to add some media to your post to capture your reader’s attention. You’ve probably heard the common saying, “A picture is worth a thousand words.” So, tell us, what would you rather do, see a picture, or read a thousand words?</p><p>In this test, Rank Math checks for the presence of images or videos in your post and warns you if it does not find any. To pass this test, all you have to do is <a href="https://backlinko.com/google-ranking-factors" target="_blank" rel="noopener">include images or videos in your post</a>. You should know that even adding a single image will help you pass the test, but you will not score 100% marks. To get a 100% score on this test, you have to add at least 4 images or videos to your post.</p><p>Please note that Rank Math checks for images <em>or</em> videos, so including any of those will pass the test.</p><p class="takeaway"><strong>Content AI:</strong> Rank Math syncs this test with the <a href="https://rankmath.com/kb/how-to-use-content-ai/#media-count">Media count recommendations</a> from Content AI. Hence, if Content AI suggests not to add images and videos, Rank Math would hide this test.</p><p>Once you pass all Content Readability tests, the screen should be something like this.</p><figure class="wp-block-image size-large is-resized"><img decoding="async" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/Content-Readability-Tests-in-Rank-Math.png" alt="Passing content readability tests" style="width:840px;height:213px"></figure><h2 class="wp-block-heading rm-has-anchor-link" id="scoring-a-full-100-for-seo-optimization"><span class="number">15</span>  Scoring a Full 100 for SEO Optimization<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#scoring-a-full-100-for-seo-optimization" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>Once you’ve passed all your tests in all the sections, check out your final SEO score.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="2560" height="651" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/overall-score-1-scaled.jpg" alt="overall seo score after passing the tests" class="wp-image-1421139"></figure><p>You might be surprised to see that you’ve passed all the tests, but still don’t score a 100/100 on Rank Math’s scale. The reason for that is that you might have passed all the tests, but you did not score 100% on all the tests. We’ve mentioned many times that you don’t have to score 100% on a test to pass it, so on the surface, it might seem that you have done all the work, but some optimizations still remain.</p><p>For example, we just mentioned in the section above that you need to add at least 4 images or videos to your post to score a 100% on the Media test. But, adding just 1 image will pass the test too.</p><p>When we demonstrated the example, we added a single image to our post to pass the test. As we added a few more images to the post, our score moved upwards.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1896" height="902" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/seo-score-after-changes.jpg" alt="the score improves after making basic changes" class="wp-image-796069"></figure><p>To score a perfect 100, go through each test’s description, and see which tests have additional conditions that require some more effort. Then go back to your post and optimize your post for those tests to score 100 on 100.</p><p>Also, pay attention to the secondary keywords. In our post, they still appear yellow, which means the post has not been optimized well for them. To see the failed tests, click on any of the secondary keywords.</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1888" height="1240" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/additional-errors.jpg" alt="checking tests for additional keywords" class="wp-image-796073"></figure><p>As you see, there are some basic and additional optimizations that we have not done for our secondary keyword. Let us quickly fix those. Once we optimized for all the recommendations for all the posts, our overall score quickly jumped to 96. Pretty amazing, right?</p><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="1862" height="928" src="./Score 100_100 With Rank Math Post Tests » Rank Math_files/final-score.jpg" alt="final score after improving optimization for all keywords" class="wp-image-796078"></figure><p>Sometimes, you will not add any secondary keywords to your posts, and to account for that, Rank Math does not score you negatively if you don’t add secondary keywords, and even the individual scores for your secondary keywords do not affect your overall score. But, the better you optimize your post for secondary keywords, the more the chances that your post will get some traffic for those keywords. So, do not ignore the optimization process for your secondary keywords.</p><h2 class="wp-block-heading">Frequently Asked Questions</h2><div id="rank-math-faq" class="rank-math-block">
<div class="rank-math-list ">
<div id="faq-question-*************" class="rank-math-list-item">
<h3 class="rank-math-question rm-has-anchor-link" id="what-is-keyword-density">What is Keyword Density?<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#what-is-keyword-density" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3>
<div class="rank-math-answer ">

<p>Keyword density refers to the frequency at which a specific keyword or phrase appears in a piece of content in relation to the total word count. It helps search engines understand the relevance and topic of the content. Rank Math looks for both the primary focus keyword and secondary focus keywords to determine your keyword density.</p>

</div>
</div>
<div id="faq-question-1695707587203" class="rank-math-list-item">
<h3 class="rank-math-question rm-has-anchor-link" id="how-do-i-add-the-focus-keyword-at-the-beginning-of-the-seo-title">How Do I Add the Focus Keyword at the Beginning of the SEO Title?<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#how-do-i-add-the-focus-keyword-at-the-beginning-of-the-seo-title" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3>
<div class="rank-math-answer ">

<p>You can add the focus keyword in the title by navigating to <strong>Rank Math’s Snippet Editor → click Rank Math icon → click Edit Snippet</strong> in the General tab of Rank Math’s meta box. Then, enter the meta title containing the focus keyword.</p>

</div>
</div>
<div id="faq-question-1700550300533" class="rank-math-list-item">
<h3 class="rank-math-question rm-has-anchor-link" id="why-does-rank-math-show-a-different-seo-score-on-page-builders"><strong>Why does Rank Math show a different SEO score on page builders?</strong><a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#why-does-rank-math-show-a-different-seo-score-on-page-builders" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h3>
<div class="rank-math-answer ">

<p>The difference in the score is due to how content is loaded in both environments (WordPress Editor and Page Builders).</p>
<p>In the case of page builders like Divi, they render shortcodes, which are special tags to execute specific functions. On the other hand, the WordPress editor does not render these shortcodes.&nbsp;Now, due to the difference in how content is rendered, there is a variation in SEO scores.&nbsp;</p>
<p>You should always refer to the score displayed inside the page builder to make your page/post fully SEO optimized.</p>

</div>
</div>
</div>
</div><h2 class="wp-block-heading rm-has-anchor-link" id="over-to-you-now">Over To You Now<a class="rm-anchor-link" href="https://rankmath.com/kb/score-100-in-tests/#over-to-you-now" aria-label="Section Anchor Link"><i class="rm-icon icon-link" aria-hidden="true"></i></a></h2><p>That’s the entire process of optimizing your post from 0 to 100 inside Rank Math. The process might seem overwhelming and complicated to you, but let us assure you, it is not. Everything that you do for the first time is tough. We promise that the process becomes easier each time you do it, and after you’ve done it a few times, it will start becoming second nature, and you’ll start doing it unconsciously.</p><p>Think of this process as learning to drive a car. When you started, you had to think about all the things consciously. But, once you learn it, your brain does everything on autopilot. Optimizing your posts is no different.</p>